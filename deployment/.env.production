# 应用基础配置
APP_NAME="拍照搜题系统"
APP_ENV=production
APP_KEY=base64:your_generated_app_key_here
APP_DEBUG=false
APP_TIMEZONE=Asia/Shanghai
APP_URL=https://your-domain.com

APP_LOCALE=zh
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=zh_CN

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

# 日志配置
LOG_CHANNEL=stack
LOG_STACK=single,daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# 数据库配置 (MySQL示例)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=paizhao_production
DB_USERNAME=paizhao_user
DB_PASSWORD=your_secure_password_here

# 缓存配置
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=.your-domain.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=lax

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=redis
CACHE_PREFIX=paizhao_

# Redis配置
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password
REDIS_PORT=6379
REDIS_DB=0

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=smtp.your-domain.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# 阿里云OSS配置
OSS_ACCESS_KEY_ID=LTAI5tQeXNXM8aBxAK3bH8NS
OSS_ACCESS_KEY_SECRET=******************************
OSS_BUCKET=lnterstellar
OSS_ENDPOINT=oss-cn-zhangjiakou.aliyuncs.com
OSS_DOMAIN=lnterstellar.oss-cn-zhangjiakou.aliyuncs.com

# 拍照搜题API配置
QUESTION_API_KEY=IwHA484058wQ1Jml8LrRECZbGRYW9Nb6
QUESTION_API_SECRET=G8Z5G4ZT7BZ5PRUnZIpsZeWqx9XvxJLHMEN15rmX3Mhp0E2B6exmysaz9FwP0gbO
QUESTION_API_URL=https://your-api-domain.com/api/v1/api/search

# 安全配置
SANCTUM_STATEFUL_DOMAINS=your-domain.com
SESSION_SECURE_COOKIE=true
SECURE_SSL_REDIRECT=true

VITE_APP_NAME="${APP_NAME}"

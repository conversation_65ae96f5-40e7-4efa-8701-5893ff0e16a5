// 获取CSRF token
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

// DOM元素
const uploadArea = document.getElementById('uploadArea');
const imageInput = document.getElementById('imageInput');
const imageUrl = document.getElementById('imageUrl');
const searchByUrlBtn = document.getElementById('searchByUrl');
const loading = document.getElementById('loading');
const errorAlert = document.getElementById('errorAlert');
const errorMessage = document.getElementById('errorMessage');
const resultContainer = document.getElementById('resultContainer');

// 初始化事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 文件上传事件
    imageInput.addEventListener('change', handleFileUpload);
    
    // URL搜索事件
    searchByUrlBtn.addEventListener('click', handleUrlSearch);
    imageUrl.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleUrlSearch();
        }
    });

    // 拖拽上传事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', function() {
        imageInput.click();
    });
});

// 处理拖拽悬停
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

// 处理文件拖拽放下
function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (validateFile(file)) {
            uploadAndSearch(file);
        }
    }
}

// 处理文件上传
function handleFileUpload() {
    const file = imageInput.files[0];
    if (file && validateFile(file)) {
        uploadAndSearch(file);
    }
}

// 处理URL搜索
function handleUrlSearch() {
    const url = imageUrl.value.trim();
    if (!url) {
        showError('请输入图片URL');
        return;
    }
    
    if (!isValidUrl(url)) {
        showError('请输入有效的URL地址');
        return;
    }
    
    searchByUrl(url);
}

// 验证文件
function validateFile(file) {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
        showError('请选择有效的图片文件 (JPG, PNG, GIF, WEBP)');
        return false;
    }
    
    if (file.size > maxSize) {
        showError('文件大小不能超过 10MB');
        return false;
    }
    
    return true;
}

// 验证URL
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// 上传文件并搜索
async function uploadAndSearch(file) {
    showLoading();
    hideError();
    hideResult();
    
    const formData = new FormData();
    formData.append('image', file);
    
    try {
        const response = await fetch('/api/upload-and-search', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken
            },
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResult(data.data, data.uploaded_image_url);
        } else {
            showError(data.message || '搜题失败，请重试');
        }
    } catch (error) {
        showError('网络错误，请检查网络连接');
        console.error('Upload error:', error);
    } finally {
        hideLoading();
    }
}

// 通过URL搜索
async function searchByUrl(url) {
    showLoading();
    hideError();
    hideResult();
    
    try {
        const response = await fetch('/api/search-by-url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({ image_url: url })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayResult(data.data);
        } else {
            showError(data.message || '搜题失败，请重试');
        }
    } catch (error) {
        showError('网络错误，请检查网络连接');
        console.error('Search error:', error);
    } finally {
        hideLoading();
    }
}

// 显示结果
function displayResult(data, uploadedImageUrl = null) {
    // 显示上传的图片
    if (uploadedImageUrl) {
        const uploadedImageContainer = document.getElementById('uploadedImageContainer');
        const uploadedImage = document.getElementById('uploadedImage');
        uploadedImage.src = uploadedImageUrl;
        uploadedImageContainer.style.display = 'block';
    }
    
    // 显示题目内容
    document.getElementById('questionText').innerHTML = data.question_text || '无题目内容';
    document.getElementById('questionType').textContent = data.question_type || '未知类型';
    
    // 显示选项
    const optionsContainer = document.getElementById('optionsContainer');
    const optionsList = document.getElementById('optionsList');
    
    if (data.options && Object.keys(data.options).length > 0) {
        optionsList.innerHTML = '';
        Object.entries(data.options).forEach(([key, value]) => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item';
            optionDiv.innerHTML = `<strong>${key}:</strong> ${value}`;
            optionsList.appendChild(optionDiv);
        });
        optionsContainer.style.display = 'block';
    } else {
        optionsContainer.style.display = 'none';
    }
    
    // 显示答案
    document.getElementById('answerText').innerHTML = data.answer || '暂无答案';
    
    // 显示解析
    document.getElementById('analysisText').innerHTML = data.analysis || '暂无解析';
    
    // 显示相关图片
    const relatedImagesContainer = document.getElementById('relatedImagesContainer');
    const relatedImages = document.getElementById('relatedImages');
    
    if (data.question_img_raw || data.question_img) {
        relatedImages.innerHTML = '';
        
        if (data.question_img_raw) {
            const img = document.createElement('img');
            img.src = data.question_img_raw;
            img.className = 'image-preview me-2';
            img.alt = '原始处理图片';
            relatedImages.appendChild(img);
        }
        
        if (data.question_img && data.question_img !== data.question_img_raw) {
            const img = document.createElement('img');
            img.src = data.question_img;
            img.className = 'image-preview';
            img.alt = '展示图片';
            relatedImages.appendChild(img);
        }
        
        relatedImagesContainer.style.display = 'block';
    } else {
        relatedImagesContainer.style.display = 'none';
    }
    
    // 显示元信息
    document.getElementById('processTime').textContent = data.process_time || 0;
    document.getElementById('cacheHit').textContent = data.cache_hit ? '是' : '否';
    
    // 显示结果容器
    resultContainer.style.display = 'block';
    
    // 滚动到结果区域
    resultContainer.scrollIntoView({ behavior: 'smooth' });
}

// 显示加载状态
function showLoading() {
    loading.style.display = 'block';
}

// 隐藏加载状态
function hideLoading() {
    loading.style.display = 'none';
}

// 显示错误
function showError(message) {
    errorMessage.textContent = message;
    errorAlert.style.display = 'block';
    errorAlert.scrollIntoView({ behavior: 'smooth' });
}

// 隐藏错误
function hideError() {
    errorAlert.style.display = 'none';
}

// 隐藏结果
function hideResult() {
    resultContainer.style.display = 'none';
}

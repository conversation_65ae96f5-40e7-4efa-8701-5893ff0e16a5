<?php

namespace App\Services;

// 移除Log依赖，避免在非Web环境下出错

class MockQuestionSearchService
{
    /**
     * 模拟搜索题目
     * 用于演示系统功能，当没有真实API时使用
     */
    public function searchQuestion(string $imageUrl): ?array
    {
        // 记录模拟搜索（避免Log依赖）
        error_log("Mock search for image: $imageUrl");
        
        // 模拟API处理时间
        usleep(500000); // 0.5秒
        
        // 返回模拟的搜题结果
        return [
            'id' => rand(10000, 99999),
            'question_text' => '下列哪个选项是正确的？',
            'question_type' => '单选题',
            'options' => [
                'A' => '选项A：这是第一个选项',
                'B' => '选项B：这是第二个选项', 
                'C' => '选项C：这是第三个选项',
                'D' => '选项D：这是第四个选项'
            ],
            'answer' => 'C',
            'analysis' => '这是一道典型的选择题。正确答案是C，因为...\n\n解题思路：\n1. 首先分析题目要求\n2. 逐一排除错误选项\n3. 确定正确答案\n\n知识点：这道题考查的是基础概念的理解和应用。',
            'cache_hit' => false,
            'process_time' => rand(300, 800),
            'question_img_raw' => $imageUrl,
            'question_img' => $imageUrl
        ];
    }

    /**
     * 格式化搜题结果
     */
    public function formatResult(array $data): array
    {
        return [
            'id' => $data['id'] ?? null,
            'question_text' => $data['question_text'] ?? $data['content'] ?? '',
            'question_type' => $data['question_type'] ?? '',
            'options' => $data['options'] ?? [],
            'answer' => $data['answer'] ?? '',
            'analysis' => $data['analysis'] ?? '',
            'cache_hit' => $data['cache_hit'] ?? false,
            'process_time' => $data['process_time'] ?? 0,
            'question_img_raw' => $data['question_img_raw'] ?? null,
            'question_img' => $data['question_img'] ?? null,
        ];
    }

    /**
     * 验证图片URL格式
     */
    public function validateImageUrl(string $url): bool
    {
        $pattern = '/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i';
        return preg_match($pattern, $url) === 1;
    }

    /**
     * 获取错误信息
     */
    public function getErrorMessage(int $code): string
    {
        $messages = [
            400 => '请求参数错误，请检查图片URL格式',
            401 => 'API密钥无效，请检查配置',
            402 => '余额不足，请充值后重试',
            403 => '账户已被冻结，请联系客服',
            429 => '请求过于频繁，请稍后重试',
            500 => '服务器繁忙，请稍后重试',
        ];

        return $messages[$code] ?? '未知错误，请联系技术支持';
    }
}

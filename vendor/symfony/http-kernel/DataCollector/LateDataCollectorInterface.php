<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\DataCollector;

/**
 * LateDataCollectorInterface.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface LateDataCollectorInterface
{
    /**
     * Collects data as late as possible.
     *
     * @return void
     */
    public function lateCollect();
}

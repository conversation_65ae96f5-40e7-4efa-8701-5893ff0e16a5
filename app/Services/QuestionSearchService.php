<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class QuestionSearchService
{
    private $apiUrl;
    private $appKey;
    private $secretKey;

    public function __construct()
    {
        $this->apiUrl = config('services.question_api.url');
        $this->appKey = config('services.question_api.key');
        $this->secretKey = config('services.question_api.secret');
    }

    /**
     * 搜索题目
     *
     * @param string $imageUrl
     * @return array|null
     */
    public function searchQuestion(string $imageUrl): ?array
    {
        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'X-App-Key' => $this->appKey,
                    'X-Secret-Key' => $this->secretKey,
                ])
                ->post($this->apiUrl, [
                    'image_url' => $imageUrl
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if ($data['code'] === 200) {
                    return $data['data'];
                } else {
                    Log::error('Question search API error: ' . $data['message']);
                    return null;
                }
            } else {
                Log::error('Question search HTTP error: ' . $response->status() . ' - ' . $response->body());
                return null;
            }
        } catch (\Exception $e) {
            Log::error('Question search exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 格式化搜题结果
     *
     * @param array $data
     * @return array
     */
    public function formatResult(array $data): array
    {
        return [
            'id' => $data['id'] ?? null,
            'question_text' => $data['question_text'] ?? $data['content'] ?? '',
            'question_type' => $data['question_type'] ?? '',
            'options' => $data['options'] ?? [],
            'answer' => $data['answer'] ?? '',
            'analysis' => $data['analysis'] ?? '',
            'cache_hit' => $data['cache_hit'] ?? false,
            'process_time' => $data['process_time'] ?? 0,
            'question_img_raw' => $data['question_img_raw'] ?? null,
            'question_img' => $data['question_img'] ?? null,
        ];
    }

    /**
     * 验证图片URL格式
     *
     * @param string $url
     * @return bool
     */
    public function validateImageUrl(string $url): bool
    {
        $pattern = '/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i';
        return preg_match($pattern, $url) === 1;
    }

    /**
     * 获取错误信息
     *
     * @param int $code
     * @return string
     */
    public function getErrorMessage(int $code): string
    {
        $messages = [
            400 => '请求参数错误，请检查图片URL格式',
            401 => 'API密钥无效，请检查配置',
            402 => '余额不足，请充值后重试',
            403 => '账户已被冻结，请联系客服',
            429 => '请求过于频繁，请稍后重试',
            500 => '服务器繁忙，请稍后重试',
        ];

        return $messages[$code] ?? '未知错误，请联系技术支持';
    }
}

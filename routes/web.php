<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\QuestionControllerSimple;

// 主页面
Route::get('/', [QuestionControllerSimple::class, 'index'])->name('question.index');

// API路由 - 使用简化版本
Route::post('/api/upload-and-search', [QuestionControllerSimple::class, 'uploadAndSearch'])->name('api.upload-search');
Route::post('/api/search-by-url', [QuestionControllerSimple::class, 'searchByUrl'])->name('api.search-url');

// 调试路由
Route::get('/debug-upload', function() {
    return view('debug-upload');
});

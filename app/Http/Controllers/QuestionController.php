<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Services\OssService;
use App\Services\QuestionSearchService;
use Illuminate\Support\Facades\Validator;

class QuestionController extends Controller
{
    private $ossService;
    private $questionSearchService;

    public function __construct(OssService $ossService, QuestionSearchService $questionSearchService)
    {
        $this->ossService = $ossService;
        $this->questionSearchService = $questionSearchService;
    }

    /**
     * 显示主页面
     */
    public function index()
    {
        return view('question.index');
    }

    /**
     * 上传图片并搜索题目
     */
    public function uploadAndSearch(Request $request): JsonResponse
    {
        // 验证请求
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:10240', // 最大10MB
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '图片格式不正确或文件过大',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            // 上传图片到OSS
            $imageUrl = $this->ossService->uploadFile($request->file('image'), 'questions');
            
            if (!$imageUrl) {
                return response()->json([
                    'success' => false,
                    'message' => '图片上传失败，请重试'
                ], 500);
            }

            // 调用搜题API
            $result = $this->questionSearchService->searchQuestion($imageUrl);
            
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => '搜题失败，请重试',
                    'image_url' => $imageUrl
                ], 500);
            }

            // 格式化结果
            $formattedResult = $this->questionSearchService->formatResult($result);

            return response()->json([
                'success' => true,
                'message' => '搜题成功',
                'data' => $formattedResult,
                'uploaded_image_url' => $imageUrl
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '系统错误：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 通过图片URL搜索题目
     */
    public function searchByUrl(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image_url' => 'required|url'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '请提供有效的图片URL',
                'errors' => $validator->errors()
            ], 400);
        }

        $imageUrl = $request->input('image_url');

        // 验证图片URL格式
        if (!$this->questionSearchService->validateImageUrl($imageUrl)) {
            return response()->json([
                'success' => false,
                'message' => '图片URL格式不正确，请确保是有效的图片链接'
            ], 400);
        }

        try {
            // 调用搜题API
            $result = $this->questionSearchService->searchQuestion($imageUrl);
            
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => '搜题失败，请检查图片URL是否可访问'
                ], 500);
            }

            // 格式化结果
            $formattedResult = $this->questionSearchService->formatResult($result);

            return response()->json([
                'success' => true,
                'message' => '搜题成功',
                'data' => $formattedResult
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '系统错误：' . $e->getMessage()
            ], 500);
        }
    }
}

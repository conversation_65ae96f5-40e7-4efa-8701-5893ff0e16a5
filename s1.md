使用lavael框架实现以下功能；


# 🚀 前端快速接入指南

## 📋 基本信息

**接口地址**: `POST /api/v1/api/search`  
**认证方式**: 请求头认证（推荐）

## 🔑 认证配置

```javascript
const API_CONFIG = {
  baseURL: 'https://your-domain.com',
  appKey: 'your_app_key',      // 从管理后台获取
  secretKey: 'your_secret_key'  // 从管理后台获取
};
```

## 📝 请求格式

### 请求头
```http
Content-Type: application/json
X-App-Key: your_app_key
X-Secret-Key: your_secret_key
```

### 请求体
```json
{
  "image_url": "https://example.com/question.jpg"
}
```

## 📤 响应格式

```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 12345,
    "question_text": "题目内容",
    "question_type": "单选题",
    "options": {
      "A": "选项A",
      "B": "选项B",
      "C": "选项C", 
      "D": "选项D"
    },
    "answer": "A:选项A",
    "analysis": "解析内容",
    "cache_hit": true,
    "process_time": 150
  }
}
```

## 💻 代码示例

### React Hook
```typescript
import { useState } from 'react';

interface QuestionData {
  id: number;
  question_text: string;
  question_type: string;
  options: Record<string, string>;
  answer: string;
  analysis: string;
  cache_hit: boolean;
  process_time: number;
}

export function useQuestionSearch() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchQuestion = async (imageUrl: string): Promise<QuestionData | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': API_CONFIG.appKey,
          'X-Secret-Key': API_CONFIG.secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });

      const result = await response.json();

      if (result.code === 200) {
        return result.data;
      } else {
        setError(result.message);
        return null;
      }
    } catch (err) {
      setError('网络请求失败');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { searchQuestion, loading, error };
}
```

### Vue 3 Composition API
```typescript
import { ref } from 'vue';

export function useQuestionSearch() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  const searchQuestion = async (imageUrl: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': API_CONFIG.appKey,
          'X-Secret-Key': API_CONFIG.secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });

      const result = await response.json();

      if (result.code === 200) {
        return result.data;
      } else {
        error.value = result.message;
        return null;
      }
    } catch (err) {
      error.value = '网络请求失败';
      return null;
    } finally {
      loading.value = false;
    }
  };

  return { searchQuestion, loading, error };
}
```

### 原生JavaScript
```javascript
async function searchQuestion(imageUrl) {
  try {
    const response = await fetch('/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': API_CONFIG.appKey,
        'X-Secret-Key': API_CONFIG.secretKey
      },
      body: JSON.stringify({ image_url: imageUrl })
    });

    const result = await response.json();

    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('搜题失败:', error);
    throw error;
  }
}

// 使用示例
searchQuestion('https://example.com/question.jpg')
  .then(data => {
    console.log('题目:', data.question_text);
    console.log('选项:', data.options);
    console.log('答案:', data.answer);
  })
  .catch(error => {
    console.error('错误:', error.message);
  });
```

## ⚠️ 错误处理

### 常见错误码
| 状态码 | 说明 | 处理方式 |
|--------|------|----------|
| 400 | 参数错误 | 检查图片URL格式 |
| 401 | 认证失败 | 检查API密钥 |
| 402 | 余额不足 | 提示用户充值 |
| 429 | 请求过频 | 延迟重试 |
| 500 | 服务器错误 | 稍后重试 |

### 错误处理示例
```javascript
function handleError(error, code) {
  switch (code) {
    case 400:
      return '图片URL格式错误';
    case 401:
      return 'API密钥无效';
    case 402:
      return '余额不足，请充值';
    case 429:
      return '请求过于频繁，请稍后重试';
    case 500:
      return '服务器繁忙，请稍后重试';
    default:
      return '未知错误';
  }
}
```

## 🎨 UI组件示例

### React组件
```tsx
import React, { useState } from 'react';
import { useQuestionSearch } from './useQuestionSearch';

export function QuestionSearchComponent() {
  const [imageUrl, setImageUrl] = useState('');
  const [result, setResult] = useState(null);
  const { searchQuestion, loading, error } = useQuestionSearch();

  const handleSearch = async () => {
    if (!imageUrl) return;
    
    const data = await searchQuestion(imageUrl);
    setResult(data);
  };

  return (
    <div className="question-search">
      <div className="input-group">
        <input
          type="url"
          value={imageUrl}
          onChange={(e) => setImageUrl(e.target.value)}
          placeholder="请输入图片URL"
          className="image-url-input"
        />
        <button 
          onClick={handleSearch} 
          disabled={loading || !imageUrl}
          className="search-button"
        >
          {loading ? '搜索中...' : '搜题'}
        </button>
      </div>

      {error && (
        <div className="error-message">
          错误: {error}
        </div>
      )}

      {result && (
        <div className="result-container">
          <h3>题目</h3>
          <p>{result.question_text}</p>
          
          <h4>选项</h4>
          <ul>
            {Object.entries(result.options).map(([key, value]) => (
              <li key={key}>{key}: {value}</li>
            ))}
          </ul>
          
          <h4>答案</h4>
          <p>{result.answer}</p>
          
          <h4>解析</h4>
          <p>{result.analysis}</p>
          
          <div className="meta-info">
            <span>缓存命中: {result.cache_hit ? '是' : '否'}</span>
            <span>处理时间: {result.process_time}ms</span>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 🔧 工具函数

### 图片URL验证
```javascript
function validateImageUrl(url) {
  const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i;
  return urlPattern.test(url);
}
```

### 防抖处理
```javascript
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const debouncedSearch = debounce(searchQuestion, 1000);
```

### 重试机制
```javascript
async function searchWithRetry(imageUrl, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await searchQuestion(imageUrl);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

## 📱 移动端适配

### 图片上传处理
```javascript
function handleImageUpload(file) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('image', file);
    
    // 上传到图片服务器
    fetch('/upload', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => resolve(data.url))
    .catch(reject);
  });
}

// 使用示例
async function handleFileSelect(event) {
  const file = event.target.files[0];
  if (file) {
    try {
      const imageUrl = await handleImageUpload(file);
      const result = await searchQuestion(imageUrl);
      // 处理结果
    } catch (error) {
      console.error('处理失败:', error);
    }
  }
}
```

## 🚀 性能优化

### 请求缓存
```javascript
const cache = new Map();

async function searchQuestionWithCache(imageUrl) {
  if (cache.has(imageUrl)) {
    return cache.get(imageUrl);
  }
  
  const result = await searchQuestion(imageUrl);
  cache.set(imageUrl, result);
  
  // 设置缓存过期时间
  setTimeout(() => cache.delete(imageUrl), 5 * 60 * 1000); // 5分钟
  
  return result;
}
```

### 并发控制
```javascript
class RequestQueue {
  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
    this.running = 0;
    this.queue = [];
  }
  
  async add(requestFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ requestFn, resolve, reject });
      this.process();
    });
  }
  
  async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    this.running++;
    const { requestFn, resolve, reject } = this.queue.shift();
    
    try {
      const result = await requestFn();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process();
    }
  }
}

const requestQueue = new RequestQueue(3);

// 使用队列
function queuedSearch(imageUrl) {
  return requestQueue.add(() => searchQuestion(imageUrl));
}
```

## 📞 获取API密钥

1. 注册账号并登录系统
2. 创建应用获取API密钥
3. 在代码中配置密钥信息
4. 开始调用接口

**管理后台地址**: `https://your-domain.com/web/admin.html`

---

**💡 提示**: 
- 请妥善保管API密钥，不要在前端代码中暴露
- 建议通过后端代理的方式调用API
- 遇到问题请查看完整的API文档或联系技术支持



1. 制作一个简单网页，上面一个按钮，点击上传图片。系统提交api请求。api会返回结果，将结果展示在界面上即可。

2. 上传的图片需要存入oss并获取链接，最终给api提交的是图片的url。

3. API返回的数据会包含1-2张图片，需要直接显示图片。

4. api可能返回多结果，或者一个结果中包含多个问题答案，具体参考api接口接入文档，来把控前端界面的展示。

阿里云Oss配置相关信息：

AccessKey ID
LTAI5tQeXNXM8aBxAK3bH8NS

AccessKey Secret
******************************

BUCKET
lnterstellar

Endpoint
oss-cn-zhangjiakou.aliyuncs.com

Bucket 域名
lnterstellar.oss-cn-zhangjiakou.aliyuncs.com



api配置相关信息：

app key
IwHA484058wQ1Jml8LrRECZbGRYW9Nb6

app Secret 
G8Z5G4ZT7BZ5PRUnZIpsZeWqx9XvxJLHMEN15rmX3Mhp0E2B6exmysaz9FwP0gbO



# 📱 拍照搜题API接入指南

## 🚀 快速开始

### 基本信息
- **接口地址**: `POST /api/v1/api/search`
- **请求格式**: `application/json`
- **响应格式**: `application/json`
- **字符编码**: `UTF-8`

### 认证方式
支持两种认证方式，推荐使用请求头认证：

#### 方式1：请求头认证（推荐）
```http
X-App-Key: your_app_key
X-Secret-Key: your_secret_key
```

#### 方式2：请求体认证（备选）
```json
{
  "image_url": "https://example.com/image.jpg",
  "app_key": "your_app_key",
  "secret_key": "your_secret_key"
}
```

## 📋 接口详情

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| image_url | string | 是 | 图片URL地址 | `https://example.com/image.jpg` |
| app_key | string | 否* | 应用密钥（请求体认证时必填） | `ak_1234567890abcdef` |
| secret_key | string | 否* | 应用秘钥（请求体认证时必填） | `sk_abcdef1234567890` |

*注：使用请求头认证时，请求体中无需包含认证信息

### 响应格式

#### 成功响应 (200)
```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 12345,
    "content": "下列哪个选项是正确的？",
    "question_type": "单选题",
    "question_text": "下列哪个选项是正确的？",
    "options": {
      "A": "选项A内容",
      "B": "选项B内容", 
      "C": "选项C内容",
      "D": "选项D内容"
    },
    "analysis": "这道题考查的是...",
    "answer": "A:选项A内容",
    "cache_hit": true,
    "process_time": 150,
    "question_img_raw": "https://example.com/processed_image.jpg",
    "question_img": "https://example.com/display_image.jpg"
  },
  "timestamp": 1703123456
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 题目唯一标识 |
| content | string | 题目内容（兼容字段，同question_text） |
| question_type | string | 题目类型（单选题、多选题、判断题等） |
| question_text | string | 题目文本内容 |
| options | object | 选项内容，key为选项标识（A/B/C/D/Y/N） |
| analysis | string | 题目解析 |
| answer | string | 正确答案（格式：选项标识:选项内容） |
| cache_hit | boolean | 是否命中缓存 |
| process_time | number | 处理耗时（毫秒） |
| question_img_raw | string | 原始处理图片URL |
| question_img | string | 展示图片URL |

## 🔧 代码示例

### JavaScript/TypeScript

#### 使用fetch（请求头认证）
```javascript
async function searchQuestion(imageUrl, appKey, secretKey) {
  try {
    const response = await fetch('/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': appKey,
        'X-Secret-Key': secretKey
      },
      body: JSON.stringify({
        image_url: imageUrl
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('搜题成功:', result.data);
      return result.data;
    } else {
      console.error('搜题失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}

// 使用示例
searchQuestion(
  'https://example.com/question.jpg',
  'your_app_key',
  'your_secret_key'
).then(data => {
  console.log('题目:', data.question_text);
  console.log('选项:', data.options);
  console.log('答案:', data.answer);
  console.log('解析:', data.analysis);
});
```

#### 使用axios（请求体认证）
```javascript
import axios from 'axios';

async function searchQuestionWithBody(imageUrl, appKey, secretKey) {
  try {
    const response = await axios.post('/api/v1/api/search', {
      image_url: imageUrl,
      app_key: appKey,
      secret_key: secretKey
    });
    
    if (response.data.code === 200) {
      return response.data.data;
    } else {
      throw new Error(response.data.message);
    }
  } catch (error) {
    if (error.response) {
      // 服务器返回错误
      throw new Error(error.response.data.message || '请求失败');
    } else {
      // 网络错误
      throw new Error('网络连接失败');
    }
  }
}
```

### React Hook示例
```typescript
import { useState, useCallback } from 'react';

interface QuestionResult {
  id: number;
  question_text: string;
  question_type: string;
  options: Record<string, string>;
  answer: string;
  analysis: string;
  cache_hit: boolean;
  process_time: number;
}

export function useQuestionSearch(appKey: string, secretKey: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const searchQuestion = useCallback(async (imageUrl: string): Promise<QuestionResult | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': appKey,
          'X-Secret-Key': secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        return result.data;
      } else {
        setError(result.message);
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求失败');
      return null;
    } finally {
      setLoading(false);
    }
  }, [appKey, secretKey]);
  
  return { searchQuestion, loading, error };
}
```

### Python示例
```python
import requests
import json

class QuestionSearchAPI:
    def __init__(self, base_url, app_key, secret_key):
        self.base_url = base_url
        self.app_key = app_key
        self.secret_key = secret_key
    
    def search_question(self, image_url):
        """搜索题目"""
        url = f"{self.base_url}/api/v1/api/search"
        
        # 使用请求头认证
        headers = {
            'Content-Type': 'application/json',
            'X-App-Key': self.app_key,
            'X-Secret-Key': self.secret_key
        }
        
        data = {
            'image_url': image_url
        }
        
        try:
            response = requests.post(url, headers=headers, json=data)
            result = response.json()
            
            if result['code'] == 200:
                return result['data']
            else:
                raise Exception(f"搜题失败: {result['message']}")
                
        except requests.RequestException as e:
            raise Exception(f"请求失败: {str(e)}")

# 使用示例
api = QuestionSearchAPI(
    base_url='https://your-domain.com',
    app_key='your_app_key',
    secret_key='your_secret_key'
)

try:
    result = api.search_question('https://example.com/question.jpg')
    print(f"题目: {result['question_text']}")
    print(f"选项: {result['options']}")
    print(f"答案: {result['answer']}")
    print(f"解析: {result['analysis']}")
except Exception as e:
    print(f"错误: {e}")
```

## ❌ 错误处理

### 错误码说明

| HTTP状态码 | 错误码 | 说明 | 处理建议 |
|-----------|--------|------|----------|
| 400 | 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 401 | 认证失败 | 检查API密钥是否正确 |
| 402 | 402 | 余额不足 | 提示用户充值 |
| 403 | 403 | 账户被冻结 | 联系客服处理 |
| 429 | 429 | 请求频率过高 | 降低请求频率，稍后重试 |
| 500 | 500 | 服务器内部错误 | 稍后重试，持续失败请联系技术支持 |

### 错误响应格式
```json
{
  "code": 401,
  "message": "无效的AppKey",
  "data": null,
  "timestamp": 1703123456
}
```

### 错误处理最佳实践
```javascript
async function handleQuestionSearch(imageUrl) {
  try {
    const result = await searchQuestion(imageUrl, appKey, secretKey);
    return result;
  } catch (error) {
    switch (error.code) {
      case 400:
        showError('请求参数错误，请检查图片URL格式');
        break;
      case 401:
        showError('认证失败，请检查API密钥');
        break;
      case 402:
        showError('余额不足，请充值后重试');
        break;
      case 403:
        showError('账户已被冻结，请联系客服');
        break;
      case 429:
        showError('请求过于频繁，请稍后重试');
        setTimeout(() => handleQuestionSearch(imageUrl), 5000);
        break;
      case 500:
        showError('服务器错误，请稍后重试');
        break;
      default:
        showError('未知错误，请联系技术支持');
    }
  }
}
```

## 🔒 安全建议

### 1. API密钥安全
- ✅ 将API密钥存储在环境变量中
- ✅ 不要在前端代码中硬编码API密钥
- ✅ 定期轮换API密钥
- ✅ 使用HTTPS传输

### 2. 请求安全
- ✅ 验证图片URL的合法性
- ✅ 限制图片大小和格式
- ✅ 实现请求重试机制
- ✅ 添加请求超时设置

### 3. 错误处理
- ✅ 不要在错误信息中暴露敏感信息
- ✅ 记录错误日志用于调试
- ✅ 为用户提供友好的错误提示

## 📊 性能优化

### 1. 缓存机制
- 系统会自动缓存相同题目的结果
- `cache_hit` 字段表示是否命中缓存
- 缓存命中的请求响应更快

### 2. 请求优化
```javascript
// 防抖处理，避免重复请求
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const debouncedSearch = debounce(searchQuestion, 1000);
```

### 3. 并发控制
```javascript
// 限制并发请求数量
class RequestQueue {
  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
    this.running = 0;
    this.queue = [];
  }
  
  async add(requestFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ requestFn, resolve, reject });
      this.process();
    });
  }
  
  async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    this.running++;
    const { requestFn, resolve, reject } = this.queue.shift();
    
    try {
      const result = await requestFn();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process();
    }
  }
}

const requestQueue = new RequestQueue(3);
```

## 🧪 测试工具

### cURL测试命令
```bash
# 请求头认证
curl -X POST "https://your-domain.com/api/v1/api/search" \
  -H "Content-Type: application/json" \
  -H "X-App-Key: your_app_key" \
  -H "X-Secret-Key: your_secret_key" \
  -d '{
    "image_url": "https://example.com/question.jpg"
  }'

# 请求体认证
curl -X POST "https://your-domain.com/api/v1/api/search" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/question.jpg",
    "app_key": "your_app_key",
    "secret_key": "your_secret_key"
  }'
```

### Postman测试
1. 创建新的POST请求
2. URL: `{{base_url}}/api/v1/api/search`
3. Headers添加认证信息或在Body中包含
4. Body选择raw JSON格式
5. 发送请求查看响应

## 📞 技术支持

如遇到技术问题，请提供以下信息：
- 请求URL和参数
- 完整的错误响应
- 请求时间戳
- 使用的编程语言和版本

联系方式：
- 技术文档：查看完整API文档
- 在线支持：访问开发者中心
- 邮件支持：<EMAIL>





---------------快速接入指南----
# 🚀 前端快速接入指南

## 📋 基本信息

**接口地址**: `POST /api/v1/api/search`  
**认证方式**: 请求头认证（推荐）

## 🔑 认证配置

```javascript
const API_CONFIG = {
  baseURL: 'https://your-domain.com',
  appKey: 'your_app_key',      // 从管理后台获取
  secretKey: 'your_secret_key'  // 从管理后台获取
};
```

## 📝 请求格式

### 请求头
```http
Content-Type: application/json
X-App-Key: your_app_key
X-Secret-Key: your_secret_key
```

### 请求体
```json
{
  "image_url": "https://example.com/question.jpg"
}
```

## 📤 响应格式

```json
{
  "code": 200,
  "message": "搜题成功",
  "data": {
    "id": 12345,
    "question_text": "题目内容",
    "question_type": "单选题",
    "options": {
      "A": "选项A",
      "B": "选项B",
      "C": "选项C", 
      "D": "选项D"
    },
    "answer": "A:选项A",
    "analysis": "解析内容",
    "cache_hit": true,
    "process_time": 150
  }
}
```

## 💻 代码示例

### React Hook
```typescript
import { useState } from 'react';

interface QuestionData {
  id: number;
  question_text: string;
  question_type: string;
  options: Record<string, string>;
  answer: string;
  analysis: string;
  cache_hit: boolean;
  process_time: number;
}

export function useQuestionSearch() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchQuestion = async (imageUrl: string): Promise<QuestionData | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': API_CONFIG.appKey,
          'X-Secret-Key': API_CONFIG.secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });

      const result = await response.json();

      if (result.code === 200) {
        return result.data;
      } else {
        setError(result.message);
        return null;
      }
    } catch (err) {
      setError('网络请求失败');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { searchQuestion, loading, error };
}
```

### Vue 3 Composition API
```typescript
import { ref } from 'vue';

export function useQuestionSearch() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  const searchQuestion = async (imageUrl: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/api/v1/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-App-Key': API_CONFIG.appKey,
          'X-Secret-Key': API_CONFIG.secretKey
        },
        body: JSON.stringify({ image_url: imageUrl })
      });

      const result = await response.json();

      if (result.code === 200) {
        return result.data;
      } else {
        error.value = result.message;
        return null;
      }
    } catch (err) {
      error.value = '网络请求失败';
      return null;
    } finally {
      loading.value = false;
    }
  };

  return { searchQuestion, loading, error };
}
```

### 原生JavaScript
```javascript
async function searchQuestion(imageUrl) {
  try {
    const response = await fetch('/api/v1/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Key': API_CONFIG.appKey,
        'X-Secret-Key': API_CONFIG.secretKey
      },
      body: JSON.stringify({ image_url: imageUrl })
    });

    const result = await response.json();

    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('搜题失败:', error);
    throw error;
  }
}

// 使用示例
searchQuestion('https://example.com/question.jpg')
  .then(data => {
    console.log('题目:', data.question_text);
    console.log('选项:', data.options);
    console.log('答案:', data.answer);
  })
  .catch(error => {
    console.error('错误:', error.message);
  });
```

## ⚠️ 错误处理

### 常见错误码
| 状态码 | 说明 | 处理方式 |
|--------|------|----------|
| 400 | 参数错误 | 检查图片URL格式 |
| 401 | 认证失败 | 检查API密钥 |
| 402 | 余额不足 | 提示用户充值 |
| 429 | 请求过频 | 延迟重试 |
| 500 | 服务器错误 | 稍后重试 |

### 错误处理示例
```javascript
function handleError(error, code) {
  switch (code) {
    case 400:
      return '图片URL格式错误';
    case 401:
      return 'API密钥无效';
    case 402:
      return '余额不足，请充值';
    case 429:
      return '请求过于频繁，请稍后重试';
    case 500:
      return '服务器繁忙，请稍后重试';
    default:
      return '未知错误';
  }
}
```

## 🎨 UI组件示例

### React组件
```tsx
import React, { useState } from 'react';
import { useQuestionSearch } from './useQuestionSearch';

export function QuestionSearchComponent() {
  const [imageUrl, setImageUrl] = useState('');
  const [result, setResult] = useState(null);
  const { searchQuestion, loading, error } = useQuestionSearch();

  const handleSearch = async () => {
    if (!imageUrl) return;
    
    const data = await searchQuestion(imageUrl);
    setResult(data);
  };

  return (
    <div className="question-search">
      <div className="input-group">
        <input
          type="url"
          value={imageUrl}
          onChange={(e) => setImageUrl(e.target.value)}
          placeholder="请输入图片URL"
          className="image-url-input"
        />
        <button 
          onClick={handleSearch} 
          disabled={loading || !imageUrl}
          className="search-button"
        >
          {loading ? '搜索中...' : '搜题'}
        </button>
      </div>

      {error && (
        <div className="error-message">
          错误: {error}
        </div>
      )}

      {result && (
        <div className="result-container">
          <h3>题目</h3>
          <p>{result.question_text}</p>
          
          <h4>选项</h4>
          <ul>
            {Object.entries(result.options).map(([key, value]) => (
              <li key={key}>{key}: {value}</li>
            ))}
          </ul>
          
          <h4>答案</h4>
          <p>{result.answer}</p>
          
          <h4>解析</h4>
          <p>{result.analysis}</p>
          
          <div className="meta-info">
            <span>缓存命中: {result.cache_hit ? '是' : '否'}</span>
            <span>处理时间: {result.process_time}ms</span>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 🔧 工具函数

### 图片URL验证
```javascript
function validateImageUrl(url) {
  const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i;
  return urlPattern.test(url);
}
```

### 防抖处理
```javascript
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const debouncedSearch = debounce(searchQuestion, 1000);
```

### 重试机制
```javascript
async function searchWithRetry(imageUrl, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await searchQuestion(imageUrl);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

## 📱 移动端适配

### 图片上传处理
```javascript
function handleImageUpload(file) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('image', file);
    
    // 上传到图片服务器
    fetch('/upload', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => resolve(data.url))
    .catch(reject);
  });
}

// 使用示例
async function handleFileSelect(event) {
  const file = event.target.files[0];
  if (file) {
    try {
      const imageUrl = await handleImageUpload(file);
      const result = await searchQuestion(imageUrl);
      // 处理结果
    } catch (error) {
      console.error('处理失败:', error);
    }
  }
}
```

## 🚀 性能优化

### 请求缓存
```javascript
const cache = new Map();

async function searchQuestionWithCache(imageUrl) {
  if (cache.has(imageUrl)) {
    return cache.get(imageUrl);
  }
  
  const result = await searchQuestion(imageUrl);
  cache.set(imageUrl, result);
  
  // 设置缓存过期时间
  setTimeout(() => cache.delete(imageUrl), 5 * 60 * 1000); // 5分钟
  
  return result;
}
```

### 并发控制
```javascript
class RequestQueue {
  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
    this.running = 0;
    this.queue = [];
  }
  
  async add(requestFn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ requestFn, resolve, reject });
      this.process();
    });
  }
  
  async process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    this.running++;
    const { requestFn, resolve, reject } = this.queue.shift();
    
    try {
      const result = await requestFn();
      resolve(result);
    } catch (error) {
      reject(error);
    } finally {
      this.running--;
      this.process();
    }
  }
}

const requestQueue = new RequestQueue(3);

// 使用队列
function queuedSearch(imageUrl) {
  return requestQueue.add(() => searchQuestion(imageUrl));
}
```

## 📞 获取API密钥

1. 注册账号并登录系统
2. 创建应用获取API密钥
3. 在代码中配置密钥信息
4. 开始调用接口

**管理后台地址**: `https://your-domain.com/web/admin.html`

---

**💡 提示**: 
- 请妥善保管API密钥，不要在前端代码中暴露
- 建议通过后端代理的方式调用API
- 遇到问题请查看完整的API文档或联系技术支持

-------------
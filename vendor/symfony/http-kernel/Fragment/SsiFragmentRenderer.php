<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\HttpKernel\Fragment;

/**
 * Implements the SSI rendering strategy.
 *
 * <AUTHOR> <<EMAIL>>
 */
class SsiFragmentRenderer extends AbstractSurrogateFragmentRenderer
{
    public function getName(): string
    {
        return 'ssi';
    }
}

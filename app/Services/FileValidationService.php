<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class FileValidationService
{
    /**
     * 验证上传的文件是否为有效图片
     * 当fileinfo扩展不可用时的备用验证方案
     */
    public function validateImageFile(UploadedFile $file): array
    {
        $errors = [];
        
        // 1. 检查文件扩展名
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = '文件扩展名不被允许';
        }
        
        // 2. 检查文件大小
        $maxSize = 10 * 1024 * 1024; // 10MB
        if ($file->getSize() > $maxSize) {
            $errors[] = '文件大小超过限制';
        }
        
        // 3. 检查文件头部签名（魔数）
        if (!$this->validateFileSignature($file)) {
            $errors[] = '文件格式不正确';
        }
        
        // 4. 尝试使用GD库验证图片
        if (!$this->validateWithGD($file)) {
            $errors[] = '不是有效的图片文件';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 通过文件头部签名验证文件类型
     */
    private function validateFileSignature(UploadedFile $file): bool
    {
        $handle = fopen($file->getRealPath(), 'rb');
        if (!$handle) {
            return false;
        }
        
        $header = fread($handle, 12);
        fclose($handle);
        
        // 检查常见图片格式的文件头
        $signatures = [
            'jpeg' => [
                "\xFF\xD8\xFF\xE0",  // JPEG JFIF
                "\xFF\xD8\xFF\xE1",  // JPEG EXIF
                "\xFF\xD8\xFF\xDB",  // JPEG
            ],
            'png' => [
                "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A", // PNG
            ],
            'gif' => [
                "GIF87a",  // GIF87a
                "GIF89a",  // GIF89a
            ],
            'webp' => [
                "RIFF", // WebP (需要进一步检查)
            ]
        ];
        
        foreach ($signatures as $type => $sigs) {
            foreach ($sigs as $sig) {
                if (substr($header, 0, strlen($sig)) === $sig) {
                    // WebP需要额外检查
                    if ($type === 'webp') {
                        return substr($header, 8, 4) === 'WEBP';
                    }
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 使用GD库验证图片
     */
    private function validateWithGD(UploadedFile $file): bool
    {
        if (!extension_loaded('gd')) {
            Log::warning('GD extension not available for image validation');
            return true; // 如果GD不可用，跳过此验证
        }
        
        try {
            $imageInfo = getimagesize($file->getRealPath());
            
            if ($imageInfo === false) {
                return false;
            }
            
            // 检查是否为支持的图片类型
            $allowedTypes = [
                IMAGETYPE_JPEG,
                IMAGETYPE_PNG,
                IMAGETYPE_GIF,
                IMAGETYPE_WEBP
            ];
            
            return in_array($imageInfo[2], $allowedTypes);
            
        } catch (\Exception $e) {
            Log::error('GD image validation failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查文件是否可能包含恶意代码
     */
    public function scanForMaliciousContent(UploadedFile $file): bool
    {
        $content = file_get_contents($file->getRealPath());
        
        // 检查常见的恶意代码模式
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec/i',
            '/base64_decode/i',
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                Log::warning('Malicious content detected in uploaded file', [
                    'pattern' => $pattern,
                    'filename' => $file->getClientOriginalName()
                ]);
                return true;
            }
        }
        
        return false;
    }
}

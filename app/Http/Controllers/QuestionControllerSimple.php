<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Services\OssService;
use App\Services\QuestionSearchService;
use App\Services\MockQuestionSearchService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class QuestionControllerSimple extends Controller
{
    private $ossService;
    private $questionSearchService;

    public function __construct(OssService $ossService)
    {
        $this->ossService = $ossService;

        // 检查是否配置了真实API
        $apiUrl = config('services.question_api.url');
        if (empty($apiUrl) || $apiUrl === 'https://your-domain.com/api/v1/api/search') {
            // 使用模拟服务
            $this->questionSearchService = new MockQuestionSearchService();
            Log::info('Using mock question search service for demonstration');
        } else {
            // 使用真实API服务
            $this->questionSearchService = new QuestionSearchService();
            Log::info('Using real question search service');
        }
    }

    /**
     * 显示主页面
     */
    public function index()
    {
        return view('question.index');
    }

    /**
     * 上传图片并搜索题目 - 简化版本
     */
    public function uploadAndSearch(Request $request): JsonResponse
    {
        // 简化的验证规则
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|max:10240', // 只检查是否为文件和大小
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '请选择有效的文件（最大10MB）',
                'errors' => $validator->errors()
            ], 400);
        }

        $file = $request->file('image');
        
        // 基础安全检查
        if (!$this->isBasicImageFile($file)) {
            return response()->json([
                'success' => false,
                'message' => '请上传图片文件（支持JPG、PNG、GIF、WebP格式）'
            ], 400);
        }

        try {
            // 上传图片到OSS
            $imageUrl = $this->ossService->uploadFile($file, 'questions');
            
            if (!$imageUrl) {
                return response()->json([
                    'success' => false,
                    'message' => '图片上传失败，请重试'
                ], 500);
            }

            // 调用搜题API
            $result = $this->questionSearchService->searchQuestion($imageUrl);
            
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => '搜题失败，请重试',
                    'image_url' => $imageUrl
                ], 500);
            }

            // 格式化结果
            $formattedResult = $this->questionSearchService->formatResult($result);

            return response()->json([
                'success' => true,
                'message' => '搜题成功',
                'data' => $formattedResult,
                'uploaded_image_url' => $imageUrl
            ]);

        } catch (\Exception $e) {
            Log::error('Upload and search error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '系统错误，请稍后重试'
            ], 500);
        }
    }

    /**
     * 通过图片URL搜索题目
     */
    public function searchByUrl(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image_url' => 'required|url'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '请提供有效的图片URL',
                'errors' => $validator->errors()
            ], 400);
        }

        $imageUrl = $request->input('image_url');

        // 验证图片URL格式
        if (!$this->questionSearchService->validateImageUrl($imageUrl)) {
            return response()->json([
                'success' => false,
                'message' => '图片URL格式不正确，请确保是有效的图片链接'
            ], 400);
        }

        try {
            // 调用搜题API
            $result = $this->questionSearchService->searchQuestion($imageUrl);
            
            if (!$result) {
                return response()->json([
                    'success' => false,
                    'message' => '搜题失败，请检查图片URL是否可访问'
                ], 500);
            }

            // 格式化结果
            $formattedResult = $this->questionSearchService->formatResult($result);

            return response()->json([
                'success' => true,
                'message' => '搜题成功',
                'data' => $formattedResult
            ]);

        } catch (\Exception $e) {
            Log::error('Search by URL error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '系统错误，请稍后重试'
            ], 500);
        }
    }

    /**
     * 基础图片文件检查
     */
    private function isBasicImageFile($file): bool
    {
        // 1. 检查文件扩展名
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedExtensions)) {
            return false;
        }

        // 2. 检查MIME类型（客户端提供的）
        $clientMimeType = $file->getClientMimeType();
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
        
        if (!in_array($clientMimeType, $allowedMimes)) {
            Log::warning("Client MIME type not allowed: $clientMimeType");
            // 不直接拒绝，因为客户端MIME类型可能不准确
        }

        // 3. 如果有fileinfo扩展，使用它验证
        if (extension_loaded('fileinfo')) {
            try {
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $realMimeType = finfo_file($finfo, $file->getRealPath());
                finfo_close($finfo);
                
                if (!in_array($realMimeType, $allowedMimes)) {
                    Log::warning("Real MIME type not allowed: $realMimeType");
                    return false;
                }
            } catch (\Exception $e) {
                Log::warning("Fileinfo check failed: " . $e->getMessage());
                // 继续其他检查
            }
        }

        // 4. 如果有GD扩展，使用它验证
        if (extension_loaded('gd')) {
            try {
                $imageInfo = getimagesize($file->getRealPath());
                if ($imageInfo === false) {
                    Log::warning("GD cannot recognize file as image");
                    return false;
                }
                
                // 检查图片类型
                $allowedTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF, IMAGETYPE_WEBP, IMAGETYPE_BMP];
                if (!in_array($imageInfo[2], $allowedTypes)) {
                    Log::warning("GD detected unsupported image type: " . $imageInfo[2]);
                    return false;
                }
            } catch (\Exception $e) {
                Log::warning("GD check failed: " . $e->getMessage());
                // 如果GD检查失败，但扩展名正确，仍然允许
            }
        }

        return true;
    }
}

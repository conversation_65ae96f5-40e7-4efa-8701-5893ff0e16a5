<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuestionController;

// 主页面
Route::get('/', [QuestionController::class, 'index'])->name('question.index');

// API路由
Route::post('/api/upload-and-search', [QuestionController::class, 'uploadAndSearch'])->name('api.upload-search');
Route::post('/api/search-by-url', [QuestionController::class, 'searchByUrl'])->name('api.search-url');
